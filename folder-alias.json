﻿{
  "src": {
    "description": "婧愪唬鐮佺洰褰?
  },
  "src/assets": {
    "description": "闈欐€佽祫婧?
  },
  "src/assets/image": {
    "description": "鍥剧墖璧勬簮"
  },
  "src/assets/style": {
    "description": "鏍峰紡鏂囦欢"
  },
  "src/components": {
    "description": "Vue缁勪欢"
  },
  "src/common": {
    "description": "鍏叡閰嶇疆"
  },
  "src/directive": {
    "description": "Vue鎸囦护"
  },
  "src/hook": {
    "description": "缁勫悎寮忓嚱鏁?
  },
  "src/router": {
    "description": "璺敱閰嶇疆"
  },
  "src/servers": {
    "description": "API鎺ュ彛"
  },
  "src/store": {
    "description": "鐘舵€佺鐞?
  },
  "src/utils": {
    "description": "宸ュ叿鍑芥暟"
  },
  "src/views": {
    "description": "椤甸潰缁勪欢"
  },
  "src/views/pageComponents": {
    "description": "椤甸潰瀛愮粍浠?
  },
  "src/views/pageComponents/clientModule": {
    "description": "鐢ㄦ埛绠＄悊妯″潡"
  },
  "src/views/pageComponents/clientModule/userList": {
    "description": "鐢ㄦ埛鍒楄〃"
  },
  "src/views/pageComponents/notifyManagement": {
    "description": "閫氱煡绠＄悊妯″潡"
  },
  "src/views/pageComponents/notifyManagement/systemNotify": {
    "description": "绯荤粺閫氱煡"
  },
  "src/views/pageComponents/systemManagement": {
    "description": "绯荤粺绠＄悊妯″潡"
  },
  "src/views/pageComponents/systemManagement/roleManagement": {
    "description": "瑙掕壊绠＄悊"
  },
  "src/views/pageComponents/systemManagement/watermarkManagement": {
    "description": "閫氱敤璁剧疆"
  },
}
