const otherRouter = [
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error/404.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    redirect: '/404',
  },
  {
    path: '/systemManagement',
    title: '系统管理',
    name: '系统管理',
    children: [
      {
        path: '/roleManagement',
        title: '角色管理',
        name: '角色管理',
        component: () => import('@/views/pageComponents/systemManagement/roleManagement/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/watermarkManagement',
        title: '通用设置',
        name: '通用设置',
        component: () => import('@/views/pageComponents/systemManagement/watermarkManagement/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/clientModule',
    title: '用户管理',
    name: '用户管理',
    children: [
      {
        path: '/userLists',
        title: '用户管理',
        name: '用户管理',
        component: () => import('@/views/pageComponents/clientModule/userList/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/notifyManagement',
    title: '通知管理',
    name: '通知管理',
    children: [
      {
        path: '/systemNotify',
        title: '系统通知',
        name: '系统通知',
        component: () => import('@/views/pageComponents/notifyManagement/systemNotify/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
]
export default otherRouter
